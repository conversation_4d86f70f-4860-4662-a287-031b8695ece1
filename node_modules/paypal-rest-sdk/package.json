{"author": "PayPal <<EMAIL>> (https://developer.paypal.com/)", "name": "paypal-rest-sdk", "description": "SDK for PayPal REST APIs", "version": "1.8.1", "homepage": "https://github.com/paypal/PayPal-node-SDK", "keywords": ["paypal", "rest", "api", "sdk"], "repository": {"type": "git", "url": "https://github.com/paypal/PayPal-node-SDK.git"}, "bugs": {"url": "https://github.com/paypal/PayPal-node-SDK/issues", "email": "<EMAIL>"}, "license": "SEE LICENSE IN https://github.com/paypal/PayPal-node-SDK/blob/master/LICENSE", "engines": {"node": ">= v0.6.0"}, "main": "./index.js", "dependencies": {"buffer-crc32": "^0.2.3", "semver": "^5.0.3"}, "devDependencies": {"blanket": "~1.1.5", "chai": "~1.9.1", "grunt": "~0.4.1", "grunt-contrib-jshint": "~0.3.0", "grunt-jsdoc": "^0.5.8", "grunt-simple-mocha": "~0.4.0", "ink-docstrap": "^0.5.2", "jsdoc": "^3.3.0-beta1", "mocha": "~1.18.2", "mocha-lcov-reporter": "0.0.1", "nock": "0.36.2"}, "config": {"blanket": {"pattern": "lib", "data-cover-never": "node_modules"}}, "scripts": {"test": "grunt"}, "readmeFilename": "README.md"}